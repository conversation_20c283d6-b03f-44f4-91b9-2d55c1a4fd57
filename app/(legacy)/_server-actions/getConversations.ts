import prisma from '@/app/libs/prismadb'
import { ConversationStatus, ConversationType } from '@prisma/client'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/app/api/auth/authOptions'
import { ConversationLayoutType } from '@/app/types'

const getConversations = async (): Promise<ConversationLayoutType[]> => {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return []
  }

  try {
    const conversations = await prisma.conversation.findMany({
      orderBy: {
        updated_at: 'desc',
      },
      where: {
        OR: [
          {
            creator_id: session.user.id,
            is_hidden: false,
            conversation_status: {
              in: [ConversationStatus.ACTIVE, ConversationStatus.COMPLETED],
            },
            conversation_type: ConversationType.ISSUE_TREE,
          },
          // {
          //   is_hidden: false,
          //   conversation_status: ConversationStatus.EXAMPLE,
          // },
        ],
      },
      select: {
        id: true,
        creator_id: true,
        conversation_status: true,
        conversation_type: true,
        created_at: true,
        updated_at: true,
        is_hidden: true,
        title: true,
        config: true,
        issue_trees: {
          orderBy: {
            updated_at: 'desc',
          },
          take: 1,
          select: {
            updated_at: true,
          },
        },
      },
    })

    return conversations.map(
      (conversation): ConversationLayoutType => ({
        ...conversation,
        config_prompt_description:
          (typeof conversation.config === 'object' &&
          conversation.config !== null &&
          'prompt' in conversation.config &&
          typeof conversation.config.prompt === 'object'
            ? (conversation.config.prompt as { description?: string })
                .description
            : null) ?? null,
        issuetree_updated_at:
          conversation.issue_trees[0]?.updated_at || conversation.updated_at,
      })
    )
  } catch (error: any) {
    console.log(error)
    return []
  }
}

export default getConversations
