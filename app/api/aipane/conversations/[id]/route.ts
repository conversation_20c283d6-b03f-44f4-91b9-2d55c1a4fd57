import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { getConversationWithMessagesPaginated } from '@/app/server-actions/ai-chat'
import { isRateLimited, getRetryAfterSeconds } from '@/app/libs/rateLimiter'

export const maxDuration = 30

/**
 * GET /api/aipane/conversations/[id]
 *
 * Retrieves a conversation with all messages and step counts (without full step data for performance).
 * Supports pagination for large conversations.
 *
 * Query parameters:
 * - limit: Number of messages to return (default: 50)
 * - cursor: Message ID to start from for pagination
 * - includeSteps: Whether to include execution steps (default: false for performance)
 */
export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: { message: 'Unauthorized', code: 'AUTH_REQUIRED' } },
        { status: 401 }
      )
    }

    // Rate limiting - skip in local development to avoid React Strict-Mode
    // double fetches causing false positives. Production environments keep
    // the protection at 30 req / min per user.
    if (process.env.NODE_ENV !== 'development') {
      const rateLimitKey = `${session.user.id}:aipane:conversation`
      if (isRateLimited(rateLimitKey, 60000)) {
        const retryAfter = getRetryAfterSeconds(rateLimitKey, 60000)
        return NextResponse.json(
          { error: { message: 'Too many requests', code: 'RATE_LIMITED' } },
          {
            status: 429,
            headers: {
              'Retry-After': retryAfter.toString(),
              'X-RateLimit-Limit': '30',
              'X-RateLimit-Remaining': '0',
            },
          }
        )
      }
    }

    const params = await context.params
    const conversationId = params.id
    const url = new URL(req.url)
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const rawCursor = url.searchParams.get('cursor')
    const cursor = rawCursor || undefined
    // Default to false for better performance - only include steps when explicitly requested
    const includeSteps = url.searchParams.get('includeSteps') === 'true'

    const startTime = Date.now()
    const timings: Record<string, number> = {}

    console.log(
      `🗨️ [Conversation API] Fetching conversation: ${conversationId}, limit: ${limit}, includeSteps: ${includeSteps}`
    )

    if (!conversationId || !conversationId.startsWith('thread_')) {
      return NextResponse.json(
        { message: 'Invalid conversation ID format' },
        { status: 400 }
      )
    }

    // Get conversation with messages using optimized paginated query
    const dbCallStart = Date.now()
    const result = await getConversationWithMessagesPaginated(conversationId, {
      limit,
      cursor,
      includeSteps,
      includeAttachments: true, // TODO(P4): switch to lazy-load attachments once attachment API is ready
    })
    timings.dbCall = Date.now() - dbCallStart

    if (!result.success) {
      return NextResponse.json({ message: result.error }, { status: 404 })
    }

    const { conversation, messages, pagination } = result.data!

    // Check if user has access to this conversation
    if (conversation.userId !== session.user.id) {
      return NextResponse.json({ message: 'Access denied' }, { status: 403 })
    }

    // Debug: log step data being returned
    console.log(
      '[Conversation API] Messages with step data:',
      messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        stepCount: msg.stepCount,
        hasSteps: !!msg.steps && msg.steps.length > 0,
      }))
    )

    const response = {
      conversation,
      messages,
      pagination,
    }

    const duration = Date.now() - startTime
    const responseSize = JSON.stringify(response).length
    timings.serialization = duration - timings.dbCall

    // Log detailed timing breakdown for debugging slow API calls
    console.log(
      `🐌 [API Performance] Conversation API breakdown for ${conversationId}:`,
      {
        total: `${duration}ms`,
        dbCall: `${timings.dbCall}ms`,
        serialization: `${timings.serialization}ms`,
        messageCount: messages.length,
        responseSize: `${(responseSize / 1024).toFixed(2)}KB`,
        includeSteps,
      }
    )

    console.log(
      `✅ [Conversation API] Retrieved ${messages.length} messages for conversation: ${conversationId} in ${duration}ms (${(responseSize / 1024).toFixed(2)}KB)`
    )

    // Add performance headers for monitoring
    const headers = new Headers()
    headers.set('X-Response-Time', `${duration}ms`)
    headers.set('X-Message-Count', messages.length.toString())
    headers.set('X-Response-Size', responseSize.toString())

    return NextResponse.json(response, { headers })
  } catch (error) {
    console.error('Conversation API error:', error)
    return NextResponse.json(
      { message: 'Error retrieving conversation' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/aipane/conversations/[id]
 *
 * Deletes a conversation and all associated messages, steps, and attachments.
 */
export async function DELETE(
  _req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const params = await context.params
    const conversationId = params.id

    if (!conversationId || !conversationId.startsWith('thread_')) {
      return NextResponse.json(
        { message: 'Invalid conversation ID format' },
        { status: 400 }
      )
    }

    // First check if conversation exists and user has access
    const result = await getConversationWithMessagesPaginated(conversationId, {
      limit: 1, // Only need to check existence, not load messages
      includeSteps: false,
      includeAttachments: false,
    })
    if (!result.success) {
      return NextResponse.json(
        { message: 'Conversation not found' },
        { status: 404 }
      )
    }

    if (result.data!.conversation.userId !== session.user.id) {
      return NextResponse.json({ message: 'Access denied' }, { status: 403 })
    }

    // TODO: Implement deleteConversation function in persistence service
    // const deleteResult = await deleteConversation(conversationId)

    console.log(
      `🗑️ [Conversation API] Conversation deletion requested: ${conversationId}`
    )

    return NextResponse.json(
      { message: 'Conversation deletion not yet implemented' },
      { status: 501 }
    )
  } catch (error) {
    console.error('Conversation deletion error:', error)
    return NextResponse.json(
      { message: 'Error deleting conversation' },
      { status: 500 }
    )
  }
}
