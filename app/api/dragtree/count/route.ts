import { NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ count: 0 })
    }
    const count = await prisma.dragTree.count({
      where: { user_id: session.user.id },
    })
    return NextResponse.json({ count })
  } catch (err) {
    return NextResponse.json({ count: 0 })
  }
}
