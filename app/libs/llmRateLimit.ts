import { NextResponse } from 'next/server'
import { type Session } from 'next-auth'
// We re-implement a simple counter-based limiter here (per user+route)
// instead of the earlier timestamp-only helper so we can allow a small
// burst of parallel calls (e.g. up to N within the window).
import { LLM_MODEL_CONFIG, type APIRoute } from '@/app/configs/llm-models'
import { SubscriptionTier } from '@prisma/client'

// In-memory store -> key → {count,startTs}
const counterMap = new Map<string, { count: number; start: number }>()

// Default 5-second window if route config does not specify
const DEFAULT_WINDOW_MS = 5_000

/**
 * Retrieve the rate-limit window for a given user tier + API route.
 * Falls back to DEFAULT_WINDOW_MS when not configured.
 */
function getWindowMs(tier: SubscriptionTier, apiRoute: APIRoute): number {
  // NOTE: undefined => fallback default window
  const routeCfg = LLM_MODEL_CONFIG?.[tier]?.[apiRoute]
  return routeCfg?.rateLimitWindowMs ?? DEFAULT_WINDOW_MS
}

function getMaxRequests(tier: SubscriptionTier, apiRoute: APIRoute): number {
  const routeCfg = LLM_MODEL_CONFIG?.[tier]?.[apiRoute]
  return routeCfg?.rateLimitMaxRequests ?? 1
}

/**
 * Enforce per-user rate limit. Returns NextResponse when blocked, or null when allowed.
 */
export async function enforceRateLimit(
  session: Session | null,
  apiRoute: APIRoute
): Promise<NextResponse | null> {
  if (!session?.user?.id) {
    return null
  }
  const userId = session.user.id
  const tier = session.user.subscription_tier ?? 'FREE'
  const windowMs = getWindowMs(tier, apiRoute)
  const maxReq = getMaxRequests(tier, apiRoute)
  const key = `${userId}:${apiRoute}`

  // ────────── Vercel KV path (if configured) ──────────
  const KV_URL = process.env.KV_REST_API_URL || process.env.KV_URL
  const KV_TOKEN =
    process.env.KV_REST_API_TOKEN || process.env.KV_REST_API_READ_ONLY_TOKEN
  if (KV_URL && KV_TOKEN) {
    try {
      // Upstash Redis REST: INCR returns the new value as plaintext
      const incrResp = await fetch(
        `${KV_URL}/incr/${encodeURIComponent(key)}`,
        {
          headers: { Authorization: `Bearer ${KV_TOKEN}` },
          cache: 'no-store',
        }
      )
      const hits = Number(await incrResp.text())
      if (hits === 1) {
        // set expiry (seconds)
        await fetch(
          `${KV_URL}/expire/${encodeURIComponent(key)}/${Math.ceil(windowMs / 1000)}`,
          {
            headers: { Authorization: `Bearer ${KV_TOKEN}` },
            cache: 'no-store',
          }
        )
      }
      if (hits > maxReq) {
        return NextResponse.json(
          {
            error: 'Too many requests – slow down.',
            retryAfter: Math.ceil(windowMs / 1000),
          },
          {
            status: 429,
            headers: { 'Retry-After': String(Math.ceil(windowMs / 1000)) },
          }
        )
      }
      return null
    } catch (err) {
      console.error('RateLimit KV error, falling back to in-memory', err)
    }
  }

  // ────────── Fallback in-memory (dev/test) ──────────
  const now = Date.now()
  const entry = counterMap.get(key)
  if (entry) {
    // within same window?
    if (now - entry.start < windowMs) {
      entry.count += 1
    } else {
      // reset window
      entry.start = now
      entry.count = 1
    }
  } else {
    counterMap.set(key, { count: 1, start: now })
  }
  const current = counterMap.get(key)!
  if (current.count > maxReq) {
    const retryAfter = Math.ceil((windowMs - (now - current.start)) / 1000)
    return NextResponse.json(
      { error: 'Too many requests – slow down.', retryAfter },
      { status: 429, headers: { 'Retry-After': String(retryAfter) } }
    )
  }
  return null
}

// Helper to reset counters (test only)
export function _resetRateLimitCounters() {
  counterMap.clear()
}
