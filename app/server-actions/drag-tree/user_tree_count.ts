'use server'

import prisma from '@/app/libs/prismadb'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'

export async function getCurrentUserDragTreeCount(): Promise<number> {
  const session = await getServerSession(authOptions)
  const userId = session?.user?.id
  if (!userId) return 0
  const count = await prisma.dragTree.count({ where: { user_id: userId } })
  return count
}
