'use server'

import { revalidatePath } from 'next/cache'
import {
  DragT<PERSON><PERSON>tatus,
  DragTreeNodeType,
  DragTreeNodeStatus,
  DragTreeNodeContentStatus,
  DragTreeNodeContentType,
} from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import {
  generateDragTreeId,
  generateDragTreeNodeId,
  generateDragTreeNodeContentId,
} from '@/app/(conv)/dragTree/[dragTreeId]/utils/id-generation'
import { getAIGenerations } from '@/app/server-actions/drag-tree/get_ai_generations'

/**
 * Get unread counts per node for efficient unread badge display
 * Returns a minimal map of nodeId -> unreadCount without shipping full content_metadata
 */
export async function getUnreadCountsByNode(dragTreeId: string) {
  // Only counts ACTIVE items whose content_metadata.isRead !== true (missing or false)
  // Using raw SQL for efficient JSONB projection without returning full JSON to client
  const rows = await prisma.$queryRaw<
    Array<{ drag_tree_node_id: string; unread_count: number }>
  >`
    SELECT drag_tree_node_id,
           SUM(CASE
                 WHEN status = 'ACTIVE'
                  AND (content_metadata->>'isRead') IS DISTINCT FROM 'true'
                 THEN 1 ELSE 0
               END)::int AS unread_count
    FROM drag_tree_node_content
    WHERE drag_tree_id = ${dragTreeId}
      AND status = 'ACTIVE'
    GROUP BY drag_tree_node_id
  `
  // Convert to plain map
  const map: Record<string, number> = {}
  for (const r of rows) {
    if (r.unread_count > 0) map[r.drag_tree_node_id] = r.unread_count
  }
  return map
}

export type CreateDragTreeInput = {
  userId: string
  title?: string
  userPrompt?: string
  preferredLanguage?: string
  metadata?: Record<string, any>
}

export type CreateDragTreeNodeInput = {
  dragTreeId: string
  nodeType: DragTreeNodeType
  label: string
  metadata?: Record<string, any>
  nodeId?: string // Allow pre-generated ID for streaming
}

export type UpdateDragTreeInput = {
  treeId: string
  title?: string
  status?: DragTreeStatus
  treeStructure?: any
  rawMarkdown?: string
  treeStructureVersion?: number
  metadata?: Record<string, any>
}

export type UpdateDragTreeNodeInput = {
  nodeId: string
  label?: string
  status?: DragTreeNodeStatus
  isInterestedIn?: boolean
  uiState?: Record<string, any>
  metadata?: Record<string, any>
}

export type CreateDragTreeNodeContentInput = {
  dragTreeId: string
  dragTreeNodeId: string
  contentType?: string
  contentVersion?: string
  contentText?: string
  contentMetadata?: Record<string, any>
  messages?: any[]
  generationMetadata?: Record<string, any>
}

export type GetDragTreeOptions = {
  /**
   * Whether to include the heavy `content_items` relation for each node. Defaults to `true` for backwards
   * compatibility. Set to `false` for a lightweight overview payload that dramatically reduces response size.
   */
  includeContentItems?: boolean
}

/**
 * Create a new drag tree
 */
export async function createDragTree(input: CreateDragTreeInput) {
  try {
    const treeId = generateDragTreeId() // Random ID for tree

    const dragTree = await prisma.dragTree.create({
      data: {
        id: treeId,
        user_id: input.userId,
        title: input.title,
        user_prompt: input.userPrompt,
        preferred_language: input.preferredLanguage || 'en',
        metadata: input.metadata || {},
        status: DragTreeStatus.INITIALIZED,
      },
      include: {
        nodes: {
          include: {
            content_items: true, // This includes all fields including content_metadata
          },
        },
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTree }
  } catch (error) {
    console.error('Error creating drag tree:', error)
    return { success: false, error: 'Failed to create drag tree' }
  }
}

/**
 * Update an existing drag tree with flexible input
 */
export async function updateDragTree(input: UpdateDragTreeInput) {
  try {
    const updateData: any = {
      updated_at: new Date(),
    }

    // Only update fields that are provided
    if (input.title) updateData.title = input.title
    if (input.status) updateData.status = input.status
    if (input.treeStructure) updateData.tree_structure = input.treeStructure
    if (input.rawMarkdown) updateData.raw_markdown = input.rawMarkdown
    if (input.treeStructureVersion)
      updateData.tree_structure_version = input.treeStructureVersion
    if (input.metadata) updateData.metadata = input.metadata

    const dragTree = await prisma.dragTree.update({
      where: { id: input.treeId },
      data: updateData,
      include: {
        nodes: {
          include: {
            content_items: true, // This includes all fields including content_metadata
          },
        },
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTree }
  } catch (error) {
    console.error('Error updating drag tree:', error)
    return { success: false, error: 'Failed to update drag tree' }
  }
}

/**
 * Create a new node in the drag tree
 * Generates deterministic ID based on tree, label, and type
 */
export async function createDragTreeNode(input: CreateDragTreeNodeInput) {
  try {
    // Use provided nodeId or generate deterministic ID for new node
    const nodeId =
      input.nodeId ||
      generateDragTreeNodeId(input.dragTreeId, input.label, input.nodeType)

    const dragTreeNode = await prisma.dragTreeNode.create({
      data: {
        id: nodeId,
        drag_tree_id: input.dragTreeId,
        node_type: input.nodeType,
        label: input.label,
        metadata: input.metadata || {},
        status: DragTreeNodeStatus.ACTIVE,
      },
      include: {
        content_items: true, // This includes all fields including content_metadata
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTreeNode }
  } catch (error) {
    console.error('Error creating drag tree node:', error)
    return { success: false, error: 'Failed to create drag tree node' }
  }
}

/**
 * Update an existing drag tree node
 */
export async function updateDragTreeNode(input: UpdateDragTreeNodeInput) {
  try {
    const updateData: any = {
      updated_at: new Date(),
    }

    // Only update fields that are provided (TypeScript handles optional types)
    if (input.label) updateData.label = input.label
    if (input.status) updateData.status = input.status
    if (input.isInterestedIn !== undefined)
      updateData.is_interested_in = input.isInterestedIn
    if (input.uiState) updateData.ui_state = input.uiState
    if (input.metadata) updateData.metadata = input.metadata

    const dragTreeNode = await prisma.dragTreeNode.update({
      where: { id: input.nodeId },
      data: updateData,
      include: {
        content_items: true, // This includes all fields including content_metadata
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTreeNode }
  } catch (error) {
    console.error('Error updating drag tree node:', error)
    return { success: false, error: 'Failed to update drag tree node' }
  }
}

/**
 * Delete a drag tree node
 */
export async function deleteDragTreeNode(nodeId: string) {
  try {
    await prisma.dragTreeNode.delete({
      where: { id: nodeId },
    })

    revalidatePath('/dragTree')
    return { success: true }
  } catch (error) {
    console.error('Error deleting drag tree node:', error)
    return { success: false, error: 'Failed to delete drag tree node' }
  }
}

/**
 * Batch create multiple drag tree nodes (useful for streaming scenarios)
 */
export async function batchCreateDragTreeNodes(
  nodes: CreateDragTreeNodeInput[]
) {
  try {
    const nodeData = nodes.map(node => ({
      id:
        node.nodeId ||
        generateDragTreeNodeId(node.dragTreeId, node.label, node.nodeType),
      drag_tree_id: node.dragTreeId,
      node_type: node.nodeType,
      label: node.label,
      metadata: node.metadata || {},
      status: DragTreeNodeStatus.ACTIVE,
    }))

    const createdDragTreeNodes = await prisma.dragTreeNode.createMany({
      data: nodeData,
      skipDuplicates: true, // Skip if ID already exists
    })

    revalidatePath('/dragTree')
    return { success: true, data: createdDragTreeNodes }
  } catch (error) {
    console.error('Error batch creating drag tree nodes:', error)
    return { success: false, error: 'Failed to batch create drag tree nodes' }
  }
}

/**
 * Get drag tree with comprehensive node and content data
 * Optimized to reduce payload size by excluding heavy content_metadata
 * Metadata can be fetched on-demand when needed by specific components
 */
export async function getDragTree(
  treeId: string,
  options: GetDragTreeOptions = { includeContentItems: true }
) {
  const { includeContentItems } = options
  try {
    const dragTree = await prisma.dragTree.findUnique({
      where: { id: treeId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            metadata: true, // Include user metadata for tutorial state
          },
        },
        nodes: {
          select: {
            id: true,
            label: true,
            node_type: true,
            status: true,
            is_interested_in: true,
            ui_state: true,
            metadata: true,
            version: true,
            content_updated_at: true,
            created_at: true,
            updated_at: true,
            // Conditionally include heavy content_items only when requested
            ...(includeContentItems
              ? {
                  content_items: {
                    select: {
                      id: true,
                      status: true,
                      content_type: true,
                      content_version: true,
                      // Re-include metadata but we'll trim it below to avoid large payload
                      content_metadata: true,
                      updated_at: true,
                    },
                  },
                }
              : {}),
          },
        },
      },
    })

    // If we included content_items, perform additional trimming to keep payload small
    if (includeContentItems && dragTree) {
      for (const node of dragTree.nodes) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore – content_items may be omitted when includeContentItems === false
        for (const item of node.content_items || []) {
          const meta: any = item.content_metadata
          if (
            meta &&
            typeof meta === 'object' &&
            Array.isArray(meta.searchResults)
          ) {
            // Deduplicate by URL and keep only the first snippet (description)
            const seen = new Set<string>()
            meta.searchResults = meta.searchResults
              .filter((res: any) => {
                if (!res || !res.url) return false
                if (seen.has(res.url)) return false
                seen.add(res.url)
                return true
              })
              .map((res: any) => ({
                url: res.url,
                icon: res.icon,
                keyword: res.keyword,
                // keep at most one snippet to greatly reduce size (description is first)
                snippets:
                  Array.isArray(res.snippets) && res.snippets.length > 0
                    ? [res.snippets[0]]
                    : [],
                timestamp: res.timestamp,
                source: res.source,
              }))
          }
        }
      }
    }

    return {
      success: true,
      data: dragTree,
      // Extract tutorial data for convenience
      userMetadata: (dragTree?.user?.metadata as Record<string, any>) || {},
    }
  } catch (error) {
    console.error('Error getting drag tree:', error)
    return { success: false, error: 'Failed to get drag tree' }
  }
}

/**
 * Get drag tree by user ID (latest one)
 */
export async function getDragTreeByUserId(userId: string) {
  try {
    const dragTree = await prisma.dragTree.findFirst({
      where: { user_id: userId },
      include: {
        nodes: {
          select: {
            id: true,
            label: true,
            node_type: true,
            status: true,
            is_interested_in: true,
            ui_state: true,
            metadata: true,
            version: true,
            content_updated_at: true,
            created_at: true,
            updated_at: true,
            content_items: {
              select: {
                id: true,
                status: true,
                content_type: true,
                content_version: true,
                content_text: true,
                content_metadata: true,
                updated_at: true,
              },
            },
          },
        },
      },
      orderBy: { created_at: 'desc' },
    })

    return { success: true, data: dragTree }
  } catch (error) {
    console.error('Error getting drag tree by user:', error)
    return { success: false, error: 'Failed to get drag tree' }
  }
}

/**
 * Create content item for a drag tree node
 */
export async function createDragTreeNodeContent(
  input: CreateDragTreeNodeContentInput
) {
  try {
    const dragTreeNodeContentId = generateDragTreeNodeContentId(
      input.dragTreeId,
      input.dragTreeNodeId
    )

    const dragTreeNodeContent = await prisma.dragTreeNodeContent.create({
      data: {
        id: dragTreeNodeContentId,
        drag_tree_id: input.dragTreeId,
        drag_tree_node_id: input.dragTreeNodeId,
        content_type: (input.contentType ||
          DragTreeNodeContentType.QUICK_RESEARCH) as DragTreeNodeContentType,
        content_version: input.contentVersion || 'v1',
        content_text: input.contentText || '',
        content_metadata: input.contentMetadata || {},
        messages: input.messages || [],
        generation_metadata: input.generationMetadata || {},
        status: DragTreeNodeContentStatus.INITIALIZED,
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTreeNodeContent }
  } catch (error) {
    console.error('Error creating drag tree node content:', error)
    return { success: false, error: 'Failed to create drag tree node content' }
  }
}

/**
 * Update drag tree title only
 */
export async function updateDragTreeTitle(treeId: string, title: string) {
  try {
    const dragTree = await prisma.dragTree.update({
      where: { id: treeId },
      data: {
        title: title,
        updated_at: new Date(),
      },
      select: {
        id: true,
        title: true,
        updated_at: true,
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTree }
  } catch (error) {
    console.error('Error updating drag tree title:', error)
    return { success: false, error: 'Failed to update drag tree title' }
  }
}

/**
 * Update drag tree preferred language only
 */
export async function updateDragTreeLanguage(
  treeId: string,
  preferredLanguage: string
) {
  try {
    const dragTree = await prisma.dragTree.update({
      where: { id: treeId },
      data: {
        preferred_language: preferredLanguage,
        updated_at: new Date(),
      },
      select: {
        id: true,
        preferred_language: true,
        updated_at: true,
      },
    })

    revalidatePath('/dragTree')
    return { success: true, data: dragTree }
  } catch (error) {
    console.error('Error updating drag tree language:', error)
    return { success: false, error: 'Failed to update drag tree language' }
  }
}

/**
 * Delete an entire drag tree and all its related data
 */
export async function deleteDragTree(treeId: string) {
  try {
    // Prisma will handle cascade deletion of nodes and content
    await prisma.dragTree.delete({
      where: { id: treeId },
    })

    revalidatePath('/dragTree')
    return { success: true }
  } catch (error) {
    console.error('Error deleting drag tree:', error)
    return { success: false, error: 'Failed to delete drag tree' }
  }
}

/**
 * Get all drag trees for a user (optimized for sidebar)
 * Only fetches essential fields needed for sidebar display
 * Sorted by latest activity across all related entities (dragTree, nodes, content)
 */
export async function getDragTreesByUserId(userId: string) {
  try {
    // Fetch only the fields needed for the sidebar and sort by drag tree\'s own updated_at.
    // This avoids loading every node & content row, significantly reducing DB egress.
    const dragTrees = await prisma.dragTree.findMany({
      where: { user_id: userId },
      select: {
        id: true,
        title: true,
        status: true,
        created_at: true,
        updated_at: true,
      },
      orderBy: { updated_at: 'desc' },
    })

    return { success: true, data: dragTrees }
  } catch (error) {
    console.error('Error getting drag trees by user:', error)
    return { success: false, error: 'Failed to get drag trees' }
  }
}

// ------------------------------------------------------------
// 🚀 Combined Initial Page Data Fetcher
// ------------------------------------------------------------
// A single server-side utility that consolidates *all* data needed
// for the first paint of the drag-tree page. By bundling the queries
// into one Promise.all we eliminate the HTTP waterfall between
// separate RSC calls and client-side API routes. This dramatically
// reduces TTFB on cold loads.
//
// Returned payload:
// {
//   dragTree        – Lightweight tree record for immediate rendering
//   userMetadata    – Parsed user metadata (tutorial flags, etc.)
//   sidebarDragTrees – List of the user's other trees for sidebar
// }
// ------------------------------------------------------------
export async function getInitialPageData(dragTreeId: string) {
  try {
    // Fetch the current session *server-side* so we can identify the user.
    const { getServerSession } = await import('next-auth')
    const { authOptions } = await import('@/app/api/auth/authOptions')

    const session = await getServerSession(authOptions)

    // We purposefully proceed even if the user is unauthenticated – the
    // upstream getDragTree call will still run but may return limited data.
    const userId = session?.user?.id

    // Enqueue all independent queries in parallel.
    const [dragTreeResult, sidebarResult, generationsResult, unreadCounts] =
      await Promise.all([
        // Fetch the *lightweight* tree (without heavy content_items) for
        // the main canvas. The client can lazily fetch content later.
        getDragTree(dragTreeId, { includeContentItems: false }),
        userId
          ? getDragTreesByUserId(userId)
          : Promise.resolve({ success: true, data: [] }),
        // Prefetch AI generations metadata for instant asset sidebar display
        userId ? getAIGenerations(dragTreeId) : Promise.resolve([]),
        // Get minimal unread counts for immediate badge display
        getUnreadCountsByNode(dragTreeId),
      ])

    // Propagate errors (if any) from the underlying helpers.
    if (!dragTreeResult.success) {
      return { success: false as const, error: dragTreeResult.error }
    }

    if (!sidebarResult.success) {
      // Sidebar is non-critical – log the error but still surface the
      // tree so the page can render. Clients can fall back to their
      // existing fetch logic if sidebar data is missing.
      console.error(
        '[getInitialPageData] Sidebar list fetch failed:',
        (sidebarResult as any).error
      )
    }

    return {
      success: true as const,
      data: {
        dragTree: dragTreeResult.data,
        userMetadata: dragTreeResult.userMetadata,
        sidebarDragTrees: sidebarResult.data ?? [],
        generations: generationsResult ?? [],
        unreadCountsByNode: unreadCounts, // Minimal unread counts for immediate badge display
      },
    }
  } catch (error) {
    console.error('[getInitialPageData] Unexpected error:', error)
    return {
      success: false as const,
      error: 'Failed to load initial page data',
    }
  }
}
