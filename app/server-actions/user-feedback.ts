'use server'

import prisma from '@/app/libs/prismadb'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import { NextResponse } from 'next/server'
import type { Prisma } from '@prisma/client'
import { UserFeedbackType } from '@prisma/client'

export type CreateUserFeedbackInput = {
  entityType: string
  entityId: string
  feedbackType: UserFeedbackType
  canContact: boolean
  feedbackText: string
  metadata: Prisma.InputJsonValue
}

export async function createUserFeedback(input: CreateUserFeedbackInput) {
  const session = await getServerSession(authOptions)
  const userId = session?.user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  const created = await prisma.userFeedback.create({
    data: {
      user_id: userId,
      feedback_type: input.feedbackType,
      entity_type: input.entityType,
      entity_id: input.entityId,
      can_contact: input.canContact,
      feedback_text: input.feedbackText,
      metadata: input.metadata,
    },
  })

  return created
}

export type GetLatestUserFeedbackParams = {
  entityType: string
  entityId: string
  feedbackType: UserFeedbackType
}

export async function getLatestUserFeedbackForEntity(
  params: GetLatestUserFeedbackParams
) {
  const session = await getServerSession(authOptions)
  const userId = session?.user?.id
  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  const latest = await prisma.userFeedback.findFirst({
    where: {
      user_id: userId,
      entity_type: params.entityType,
      entity_id: params.entityId,
      feedback_type: params.feedbackType,
    },
    orderBy: { created_at: 'desc' },
    select: {
      id: true,
      created_at: true,
    },
  })

  return latest
}
