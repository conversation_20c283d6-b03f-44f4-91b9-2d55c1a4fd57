'use client'

import { useState } from 'react'
import { useChat } from '@ai-sdk/react'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
// import { Tool } from '@/components/ai-elements/tool'
// import { Reasoning } from '@/components/ai-elements/reasoning'
import {
  PromptInput,
  PromptInputTextarea,
  PromptInputSubmit,
} from '@/components/ai-elements/prompt-input'

export default function ChatDemoPage() {
  const [input, setInput] = useState('')

  const { messages, sendMessage, status, error } = useChat({
    api: '/api/chat-demo',
    onError: error => {
      console.error('Chat error:', error)
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (input.trim() && status !== 'streaming') {
      sendMessage({ text: input })
      setInput('')
    }
  }

  return (
    <div className="flex h-screen flex-col bg-background">
      {/* Header */}
      <div className="border-b border-border bg-card px-6 py-4">
        <h1 className="text-xl font-semibold text-foreground">
          AI SDK Elements Chat Demo
        </h1>
        <p className="text-sm text-muted-foreground">
          ChatGPT-style interface with tool calling and reasoning support
        </p>
      </div>

      {/* Chat Container */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <Conversation className="flex-1">
          <ConversationContent>
            {messages.length === 0 && (
              <div className="flex h-full items-center justify-center">
                <div className="text-center">
                  <h2 className="text-lg font-medium text-muted-foreground">
                    Start a conversation
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Try asking about the weather, or request a calculation
                  </p>
                </div>
              </div>
            )}

            {messages.map(message => (
              <Message key={message.id} from={message.role}>
                <MessageContent>
                  {/* Render message parts */}
                  {message.parts.map((part, index) => {
                    if (part.type === 'text') {
                      return (
                        <Response key={`${message.id}-text-${index}`}>
                          {part.text}
                        </Response>
                      )
                    }

                    // Handle tool calls - simplified for now
                    if (part.type.startsWith('tool-')) {
                      return (
                        <div
                          key={`${message.id}-tool-${index}`}
                          className="mt-2 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950"
                        >
                          <div className="flex items-center gap-2 text-sm font-medium text-blue-700 dark:text-blue-300">
                            🔧 Tool: {part.type}
                          </div>
                          <pre className="mt-2 text-xs text-blue-600 dark:text-blue-400">
                            {JSON.stringify(part, null, 2)}
                          </pre>
                        </div>
                      )
                    }

                    return null
                  })}
                </MessageContent>
              </Message>
            ))}

            {error && (
              <div className="mx-4 rounded-lg border border-destructive/20 bg-destructive/10 p-4">
                <p className="text-sm text-destructive">
                  Error: {error.message}
                </p>
              </div>
            )}
          </ConversationContent>
          <ConversationScrollButton />
        </Conversation>

        {/* Input Area */}
        <div className="border-t border-border bg-card p-4">
          <PromptInput onSubmit={handleSubmit} className="mx-auto max-w-4xl">
            <PromptInputTextarea
              value={input}
              onChange={e => setInput(e.target.value)}
              placeholder="Type your message here..."
              className="min-h-[60px] resize-none pr-12"
              disabled={status === 'streaming'}
            />
            <PromptInputSubmit
              status={status === 'streaming' ? 'streaming' : 'ready'}
              disabled={!input.trim() || status === 'streaming'}
              className="absolute bottom-2 right-2"
            />
          </PromptInput>
        </div>
      </div>
    </div>
  )
}
